# Dokumentasi Implementasi Fitur Buku Profil Desa

## Deskripsi
Fitur ini menambahkan kemampuan untuk mengupload dan menampilkan buku profil desa dalam format PDF pada sistem informasi desa. Fitur ini terintegrasi dengan semua tema yang tersedia (landing, profile, progides).

## Fitur yang Ditambahkan

### 1. Upload Buku Profil Desa
- Input file upload di halaman Setting Umum
- Validasi file hanya menerima format PDF
- Maksimal ukuran file 10MB
- Preview file yang sudah diupload

### 2. PDF Viewer
- Menampilkan PDF dalam iframe
- Fallback untuk browser yang tidak mendukung PDF viewer
- Tombol download PDF
- Responsive design untuk semua tema

### 3. Navigasi Menu
- Menu "Profil Desa" ditambahkan di semua tema
- Menu hanya muncul jika file PDF sudah diupload
- Terintegrasi dengan navigasi utama dan footer

## File yang Dimodifikasi/Ditambahkan

### Database
- **File**: `database_update_buku_profil_desa.sql`
- **Perubahan**: Menambahkan kolom `buku_profil_desa` ke tabel `setting_umum`

### Controller
- **File**: `app/application/controllers/BukuProfilDesa.php` (BARU)
- **Fungsi**: 
  - `index()`: Menampilkan PDF viewer
  - `download()`: Download file PDF

- **File**: `app/application/controllers/SettingUmum.php`
- **Perubahan**: Menambahkan logic upload PDF di method `process_update()`

### Views

#### Setting Umum
- **File**: `app/application/views/settingumum.php`
- **Perubahan**: 
  - Menambahkan input upload PDF
  - Validasi JavaScript untuk file PDF
  - Preview file yang sudah diupload

#### PDF Viewer Views
- **File**: `app/application/views/landing/buku_profil_desa.php` (BARU)
- **File**: `app/application/views/profile/buku_profil_desa.php` (BARU)
- **File**: `app/application/views/progides/buku_profil_desa.php` (BARU)

#### Master Templates (Navigasi)
- **File**: `app/application/views/landing/master.php`
- **File**: `app/application/views/profile/master.php`
- **File**: `app/application/views/progides/master.php`
- **Perubahan**: Menambahkan menu "Profil Desa" dengan kondisi

### Routes
- **File**: `app/application/config/routes.php`
- **Perubahan**: Menambahkan route untuk `buku-profil-desa` dan `buku-profil-desa/download`

## Cara Penggunaan

### 1. Upload Buku Profil Desa
1. Login sebagai admin desa
2. Masuk ke menu "Setting Umum"
3. Scroll ke bagian "Informasi Lainnya"
4. Upload file PDF di field "Buku Profil Desa"
5. Klik "Simpan"

### 2. Melihat Buku Profil Desa
1. Setelah file diupload, menu "Profil Desa" akan muncul di navigasi
2. Klik menu "Profil Desa" untuk melihat PDF viewer
3. Gunakan tombol "Download PDF" untuk mengunduh file

## Validasi dan Keamanan

### Upload File
- Hanya menerima file dengan ekstensi .pdf
- Validasi MIME type: application/pdf
- Maksimal ukuran file: 10MB
- File disimpan menggunakan cloud storage yang sudah ada

### Akses File
- File hanya dapat diakses jika sudah diupload
- Menggunakan sistem autentikasi yang sudah ada
- 404 error jika file tidak ditemukan

## Kompatibilitas Tema

### Landing Theme
- Menggunakan Bootstrap 4
- Icon Font Awesome 4
- Layout: Container dengan background blur effect
- Hero section dengan title dan subtitle
- Card dengan shadow untuk PDF viewer
- Responsive design

### Profile Theme
- Menggunakan Bootstrap 4
- Icon Font Awesome 5
- Layout: Split screen (5/7 columns)
- Left side: Info dengan logo desa
- Right side: PDF viewer dalam card
- Background abu-abu untuk left section

### ProGides Theme
- Menggunakan Tailwind CSS
- Icon Font Awesome 6
- Layout: Hero section + split content
- Hero dengan background image dari Unsplash
- Left side: Info card dengan logo desa
- Right side: PDF viewer
- Modern design dengan rounded corners

## URL dan Route

- **PDF Viewer**: `/buku-profil-desa`
- **Download PDF**: `/buku-profil-desa/download`

## Fallback dan Error Handling

### Browser Compatibility
- Iframe PDF viewer untuk browser modern
- Fallback message untuk browser yang tidak mendukung
- Link alternatif untuk membuka PDF di tab baru

### Error Handling
- 404 jika file tidak ditemukan
- Validasi file di frontend dan backend
- Error message yang user-friendly

## Testing

### Test Cases
1. Upload file PDF valid
2. Upload file non-PDF (harus ditolak)
3. Upload file > 10MB (harus ditolak)
4. Akses PDF viewer dengan file yang ada
5. Akses PDF viewer tanpa file (404)
6. Download PDF
7. Menu visibility berdasarkan ketersediaan file

### Browser Testing
- Chrome (PDF viewer native)
- Firefox (PDF viewer native)
- Safari (PDF viewer native)
- Edge (PDF viewer native)
- Mobile browsers

## Maintenance

### Backup
- File PDF disimpan di cloud storage
- Nama file disimpan di database
- Backup database mencakup referensi file

### Update
- Untuk mengganti file PDF, upload file baru di Setting Umum
- File lama akan tertimpa secara otomatis

## Troubleshooting

### PDF Tidak Tampil
1. Cek apakah file sudah diupload di Setting Umum
2. Cek koneksi internet untuk cloud storage
3. Cek browser support untuk PDF viewer
4. Gunakan fallback link jika iframe tidak bekerja

### Menu Tidak Muncul
1. Pastikan file PDF sudah diupload
2. Clear cache browser
3. Cek kondisi `!empty($setting->buku_profil_desa)` di template

### Upload Gagal
1. Cek ukuran file (max 10MB)
2. Cek format file (harus PDF)
3. Cek koneksi cloud storage
4. Cek permission upload
