<?php
defined('BASEPATH') or die('No direct script access allowed!');

class BukuProfilDesa extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Setting_Umum', 'settingumum');
    }

    public function index()
    {
        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        
        if ($setting->num_rows() == 0) {
            return $this->load->view('profile_v2/configuration');
        }

        $setting_data = $setting->row();
        
        // Check if buku profil desa exists
        if (empty($setting_data->buku_profil_desa)) {
            show_404();
        }

        $data = array();
        $data['title'] = 'Buku Profil Desa';
        $data['setting'] = $setting_data;
        $data['pdf_url'] = asset_url($setting_data->buku_profil_desa);

        // Determine which theme to use based on platform and theme settings
        if (getPlatformName() == 'ProGides') {
            $data['content'] = 'progides/buku_profil_desa';
            return $this->load->view('progides/master', $data);
        } elseif ($this->subdomain_account->themeid == 2) {
            $data['content'] = 'profile/buku_profil_desa';
            return $this->load->view('profile/master', $data);
        } else {
            $data['content'] = 'landing/buku_profil_desa';
            return $this->load->view('landing/master', $data);
        }
    }

    public function download()
    {
        $where = array(
            'a.id_user' => $this->subdomain_account->id
        );

        $setting = $this->settingumum->getDefaultData($where);
        
        if ($setting->num_rows() == 0) {
            show_404();
        }

        $setting_data = $setting->row();
        
        // Check if buku profil desa exists
        if (empty($setting_data->buku_profil_desa)) {
            show_404();
        }

        // Redirect to the PDF file for download
        redirect(asset_url($setting_data->buku_profil_desa));
    }
}
