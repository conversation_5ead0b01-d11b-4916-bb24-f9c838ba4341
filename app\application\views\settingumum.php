<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <form id="frmSettingUmum" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Informasi Pemberi Sambutan</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label>Logo <?= is<PERSON><PERSON><PERSON>han() ? 'Kelurahan' : 'Desa' ?></label>
                            <input type="file" name="logo_desa" class="form-control" <?= getValueObj($setting, 'logo_desa') == null ? 'required' : null ?> accept=".jpg,.jpeg,.png">

                            <div class="mt-2">
                                <p class="mb-1">Preview Logo Kelurahan</p>
                                <div style="border: 1px solid #ccc;" class="d-inline-block mb-2">
                                    <img src="<?= asset_url(getValueObj($setting, 'logo_desa')) ?>" alt="" width="100" class="preview-logo">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Nama <?= isKelurahan() ? 'Lurah' : 'Kepala Desa' ?></label>
                            <input type="text" name="nama" class="form-control" placeholder="Nama Pemberi Sambutan" value="<?= getValueObj($setting, 'nama') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Foto <?= isKelurahan() ? 'Lurah' : 'Kepala Desa' ?></label>
                            <input type="file" name="foto" class="form-control" <?= getValueObj($setting, 'foto') == null ? 'required' : null ?>>

                            <div class="mt-2">
                                <p class="mb-1">Preview Foto <?= isKelurahan() ? 'Lurah' : 'Kepala Desa' ?></p>
                                <div style="border: 1px solid #ccc;" class="d-inline-block mb-2">
                                    <img src="<?= asset_url(getValueObj($setting, 'foto')) ?>" alt="" width="100" class="preview-foto">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Isi Sambutan</label>
                            <textarea name="isi" id="isi" cols="10" rows="10" required><?= getValueObj($setting, 'isi') ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Pemerintahan</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label>Visi</label>
                            <textarea name="visi" id="visi" cols="10" rows="10" required><?= getValueObj($pemerintahan, 'visi') ?></textarea>
                        </div>

                        <div class="form-group">
                            <label>Misi</label>
                            <textarea name="misi" id="misi" cols="10" rows="10" required><?= getValueObj($pemerintahan, 'misi') ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Informasi Wilayah</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label>Luas Tanah Kas (<small>m<sup>2</sup></small>)</label>
                            <input type="number" name="luas_tanah_kas" class="form-control" placeholder="Luas Tanah Kas" value="<?= getValueObj($setting, 'luas_tanah_kas') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Luas Tanah Desa (<small>m<sup>2</sup></small>)</label>
                            <input type="number" name="luas_tanah_desa" class="form-control" placeholder="Luas Tanah Desa" value="<?= getValueObj($setting, 'luas_tanah_desa') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Luas DHKP (<small>m<sup>2</sup></small>)</label>
                            <input type="number" name="luas_dhkp" class="form-control" placeholder="Luas DHKP" value="<?= getValueObj($setting, 'luas_dhkp') ?>" required>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">SEO</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label for="">Deskripsi Website (Opsional)</label>
                            <textarea name="description" class="form-control" placeholder="Masukkan Deskripsi Website"><?= getValueObj($setting, 'description') ?></textarea>
                        </div>

                        <div class="form-group">
                            <label>Keyword (Opsional)</label>
                            <input type="text" name="keywords" class="form-control" placeholder="Masukkan Keyword" value="<?= getValueObj($setting, 'keywords') ?>">
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Media Sosial</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label>Facebook</label>
                            <input type="text" name="facebook" class="form-control" placeholder="Facebook" value="<?= getValueObj($setting, 'facebook') ?>">
                        </div>

                        <div class="form-group">
                            <label>Twitter</label>
                            <input type="text" name="twitter" class="form-control" placeholder="Twitter" value="<?= getValueObj($setting, 'twitter') ?>">
                        </div>

                        <div class="form-group">
                            <label>Youtube</label>
                            <input type="text" name="youtube" class="form-control" placeholder="Youtube" value="<?= getValueObj($setting, 'youtube') ?>">
                        </div>

                        <div class="form-group">
                            <label>Instagram</label>
                            <input type="text" name="instagram" class="form-control" placeholder="Instagram" value="<?= getValueObj($setting, 'instagram') ?>">
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Surat Menyurat</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label for="">Kode Desa</label>
                            <input type="text" name="kodedesa" class="form-control" placeholder="Masukkan Kode Desa" value="<?= getValueObj($setting, 'kode_desa') ?>">
                        </div>

                        <div class="alert alert-info">
                            <p class="mb-1"><b>Format Surat</b></p>
                            <ul class="mb-0">
                                <li><b>${kode_surat}</b>: Variabel ini digunakan untuk menampilkan <b>Kode Surat</b> setiap kali surat dibuat</li>
                                <li><b>${tahun}</b> : Variabel ini digunakan untuk menampilkan <b>Tahun Pembuatan Surat</b></li>
                                <li><b>${bulan}</b> : Variabel ini digunakan untuk menampilkan <b>Bulan Pembuatan Surat</b></li>
                                <li><b>${tanggal}</b> : Variabel ini digunakan untuk menampilkan <b>Tanggal Pembuatan Surat</b></li>
                                <li><b>${bulan_rom}</b> : Variabel ini digunakan untuk menampilkan <b>Bulan Pembuatan Surat</b> (Dalam bentuk Romawi)</li>
                                <li><b>${sequence}</b> : Variabel ini digunakan untuk menampilkan <b>Nomor Urut Pembuatan Surat</b> (Dikalkulasi dalam waktu 1 Bulan, Direset dibulan berikutnya)</li>
                                <li><b>${kode_desa}</b> : Variabel ini digunakan untuk menampilkan <b>Kode Desa</b></li>
                            </ul>
                        </div>

                        <div class="form-group">
                            <label for="">Format Surat</label>
                            <input type="text" name="formatsurat" class="form-control" placeholder="Format Surat" value="<?= getValueObj($setting, 'format_surat') ?>">
                            <small>*Contoh: ${sequence}/${kode_desa}/${kode_surat}/${tanggal}/${bulan_rom}/${tahun}</small>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Informasi Lainnya</h4>
                    </div>

                    <div class="card-body">
                        <div class="form-group">
                            <label><?= isKelurahan() ? 'Kelurahan' : 'Desa' ?></label>
                            <input type="text" name="desa" class="form-control" placeholder="Desa" value="<?= getValueObj($setting, 'desa') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Alamat Kantor</label>
                            <input type="text" name="alamat" class="form-control" placeholder="Alamat Kantor" value="<?= getValueObj($setting, 'alamat') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Kecamatan</label>
                            <input type="text" name="kecamatan" class="form-control" placeholder="Kecamatan" value="<?= getValueObj($setting, 'kecamatan') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Kabupaten/Kota</label>
                            <input type="text" name="kabupaten" class="form-control" placeholder="Kabupaten" value="<?= getValueObj($setting, 'kabupaten') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Provinsi</label>
                            <input type="text" name="provinsi" class="form-control" placeholder="Provinsi" value="<?= getValueObj($setting, 'provinsi') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Kode Pos</label>
                            <input type="text" name="kodepos" class="form-control" placeholder="Kode Pos" value="<?= getValueObj($setting, 'kodepos') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Alamat Email</label>
                            <input type="email" name="email" class="form-control" placeholder="Alamat Email" value="<?= getValueObj($setting, 'email') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Jam Kerja Kantor</label>
                            <input type="text" name="jam_kerja" class="form-control" placeholder="Jam Kerja Kantor" value="<?= getValueObj($setting, 'jam_kerja') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Nomor Telepon</label>
                            <input type="number" name="kontak" class="form-control" placeholder="Nomor Telepon" value="<?= getValueObj($setting, 'kontak') ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Buku Profil Desa</label>
                            <input type="file" name="buku_profil_desa" class="form-control" accept=".pdf">
                            <small class="text-muted">*Hanya file PDF yang diperbolehkan</small>

                            <?php if (getValueObj($setting, 'buku_profil_desa') != null): ?>
                                <div class="mt-2">
                                    <p class="mb-1">File Saat Ini:</p>
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-file-pdf-o text-danger mr-2"></i>
                                        <a href="<?= asset_url(getValueObj($setting, 'buku_profil_desa')) ?>" target="_blank" class="text-primary">
                                            <?= getValueObj($setting, 'buku_profil_desa') ?>
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-check form-check-inline mt-2">
                            <input class="form-check-input" type="checkbox" name="slider_news" id="slider_news" value="1" <?= getValueObj($setting, 'slider_news') == '1' ? 'checked' : null ?>>
                            <label class="form-check-label" for="slider_news">Tampilkan Berita Terbaru pada Slider Website</label>
                        </div>
                    </div>
                </div>

                <div class="float-right">
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>

                <div class="clearfix"></div>
            </form>
        </div>
    </div>
</div>
<!-- ============================================================== -->
<!-- End Container fluid  -->
<!-- ============================================================== -->

<script>
    window.onload = function() {
        $(function() {
            CKEDITOR.replace('isi');
            CKEDITOR.replace('visi');
            CKEDITOR.replace('misi');
        });

        $('input[name=keywords]').tagsinput({
            tagClass: 'badge badge-primary'
        });

        $('input[name=keywords]').parent().find('.bootstrap-tagsinput input').keypress(function(e) {
            if (e.which == 13) {
                e.preventDefault();
            }
        });

        $('input[name=logo_desa]').change(function() {
            let file = $(this).prop('files')[0];

            if (file == undefined) {
                return $('.preview-logo').attr('src', '<?= asset_url(getValueObj($setting, 'logo_desa')) ?>');
            }

            let fileType = file.type;
            let validImageTypes = ["image/jpg", "image/jpeg", "image/png"];

            if ($.inArray(fileType, validImageTypes) < 0) {
                return swalMessageFailed('File yang diupload bukan gambar!');
            }

            // Preview image
            let reader = new FileReader();
            reader.onload = function(e) {
                $('.preview-logo').attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        });

        $('input[name=foto]').change(function() {
            let file = $(this).prop('files')[0];

            if (file == undefined) {
                return $('.preview-foto').attr('src', '<?= asset_url(getValueObj($setting, 'foto')) ?>');
            }

            let fileType = file.type;
            let validImageTypes = ["image/jpg", "image/jpeg", "image/png"];

            if ($.inArray(fileType, validImageTypes) < 0) {
                return swalMessageFailed('File yang diupload bukan gambar!');
            }

            // Preview image
            let reader = new FileReader();
            reader.onload = function(e) {
                $('.preview-foto').attr('src', e.target.result);
            }
            reader.readAsDataURL(file);
        });

        $('input[name=buku_profil_desa]').change(function() {
            let file = $(this).prop('files')[0];

            if (file == undefined) {
                return;
            }

            let fileType = file.type;
            let validPdfTypes = ["application/pdf"];

            if ($.inArray(fileType, validPdfTypes) < 0) {
                $(this).val('');
                return swalMessageFailed('File yang diupload harus berformat PDF!');
            }

            // Check file size (max 10MB)
            let maxSize = 10 * 1024 * 1024; // 10MB in bytes
            if (file.size > maxSize) {
                $(this).val('');
                return swalMessageFailed('Ukuran file tidak boleh lebih dari 10MB!');
            }
        });

        $('#frmSettingUmum').submit(function(e) {
            e.preventDefault();

            let isi = CKEDITOR.instances.isi.getData();
            let visi = CKEDITOR.instances.visi.getData();
            let misi = CKEDITOR.instances.misi.getData();
            let formData = new FormData(this);
            formData.set('isi', isi);
            formData.set('visi', visi);
            formData.set('misi', misi);

            let elementsForm = $(this).find('button, input');

            elementsForm.attr('disabled', true);

            $.ajax({
                url: $(this).attr('action'),
                method: $(this).attr('method'),
                dataType: 'json',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    elementsForm.removeAttr('disabled');

                    if (response.RESULT == 'OK') {
                        return swalMessageSuccess(response.MESSAGE, ok => {
                            window.location.reload();
                        });
                    } else {
                        return swalMessageFailed(response.MESSAGE);
                    }
                }
            }).fail(function() {
                elementsForm.removeAttr('disabled');

                return swalError();
            });
        });
    };
</script>