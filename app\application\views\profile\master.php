<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <title><?= isset($title) ? $title : 'Default Title' ?> - <?= isset($setting->desa) ? 'Desa ' . $setting->desa : null ?></title>
    <meta name="description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
    <meta name="keywords" content="<?= isset($setting->keywords) ? $setting->keywords : 'platform desa digital, portal desa digital, aplikasi digital desa, desa digital adalah, bip desa, digital desa, digital desa online, aplikasi pelayanan desa online, kriteria desa digital, layanan desa, login desa digital, web pelayanan desa, online digital desa, profil desa digital, rab desa digital, smart desa digital, download portal desa digital, website desa online, cara membuat desa digital, program desa digital 2021, contoh program desa digital, membangun desa digital, sistem desa digital, desa digital di indonesia, program desa digital, proposal desa digital, desa wisata digital adalah, aplikasi desa digital, apa itu desa digital, anggaran desa digital, ayo bangun desa digital, artikel desa digital, arti desa digital, apa keuntungan kelemahan peluang dan ancaman dalam menerapkan desa digital, desa berbasis digital, desa wisata berbasis digital, bumdes desa digital, contoh desa digital, ciri ciri desa digital, cara login desa digital, desa cerdas digital, ekonomi digital desa, gambar desa digital, inovasi desa digital, jumlah desa digital di indonesia, daftar desa digital di indonesia, konsep desa digital, kelebihan dan kekurangan desa digital, kriteria desa digital, kategori desa digital, desa digital login, login page desa digital, latar belakang desa digital, logo desa digital, manfaat desa digital, membangun desa digital, maksud dan tujuan desa digital, memajukan pariwisata desa melalui media digital, sistem desa digital, syarat menjadi desa digital, sistem informasi desa digital, sosialisasi desa digital, desa digital terbaik, template desa digital, target desa digital, teknologi desa digital, website desa digital, desa wisata digital di indonesia desa digital 2022, program digitalisasi desa, surat desa otomatis, aplikasi surat menyurat desa otomatis, format surat desa, kode surat desa, kop surat desa doc, logo surat desa, penomoran surat desa, nomor surat otomatis, surat menyurat desa excel, tata naskah surat desa, nomor surat tugas desa, nomor agenda surat desa' ?>">
    <meta name="robots" content="index, follow">

    <link href="https://gides.id/assets/img/favicon.png" rel="icon">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
    <?php if (!isset($berita)) : ?>
        <meta property="og:url" content="<?= base_url() ?>">
        <meta property="og:title" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
        <meta property="og:description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
        <meta property="og:image" itemprop="image" content="">
    <?php elseif (isset($berita->id) && isset($berita->judul) && isset($berita->deskripsi)) : ?>
        <meta property="og:url" content="<?= base_url(uri_string() . '?id=' . $berita->id) ?>">
        <meta property="og:title" content="<?= $berita->judul ?>">
        <meta property="og:description" content="<?= extract_text($berita->deskripsi) ?>">
        <meta property="og:image" itemprop="image" content="<?= asset_url($berita->foto) ?>">
    <?php endif; ?>

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <?php if (!isset($berita)) : ?>
        <meta property="twitter:url" content="<?= base_url() ?>">
        <meta property="twitter:title" content="<?= isset($setting->desa) ? $setting->desa : 'Go Digital Desa' ?>">
        <meta property="twitter:description" content="<?= isset($setting->description) ? $setting->description : 'Gides adalah singkatan dari Go Digital Desa. Kami membantu desa memiliki website profil yang menyajikan konten menarik seperti profil pemerintahan, kegiatan, data statistik, dan potensi wisata. Tujuannya agar desa dapat memaksimalkan potensi digital dan memberi dampak positif.' ?>">
        <meta property="twitter:image" itemprop="image" content="">
    <?php elseif (isset($berita->id) && isset($berita->judul) && isset($berita->deskripsi)) : ?>
        <meta property="twitter:url" content="<?= base_url(uri_string() . '?id=' . $berita->id) ?>">
        <meta property="twitter:title" content="<?= $berita->judul ?>">
        <meta property="twitter:description" content="<?= extract_text($berita->deskripsi) ?>">
        <meta property="twitter:image" itemprop="image" content="<?= asset_url($berita->foto) ?>">
    <?php endif; ?>

    <link rel="stylesheet" href="<?= asset_url() ?>assets/templates/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templates/css/style.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templates/css/apb-desa.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templates/css/style-kjadopt.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/extra-libs/lightbox/dist/css/lightbox.min.css">

    <link rel="stylesheet" href="<?= asset_url() ?>assets/templates/plugins/owl/owl.carousel.css">
    <link rel="stylesheet" href="<?= asset_url() ?>assets/templates/plugins/owl/owl.transitions.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick-theme.css" />

    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css" integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous" />

    <link rel="canonical" href="<?= canocial_beautifier(base_url(uri_string())) ?>">

    <style>
        .navbar.bg-green,
        .misi,
        .footer,
        .infografis .penduduk,
        .infografis .bg-green {
            background-color: #0A142F !important;
        }

        .nav-item {
            margin-left: unset !important;
        }

        .infografis .jumlah {
            color: white;
        }

        .btn.btn-outline-green {
            border: 2px solid #0A142F;
            color: #0A142F;
        }

        .demo .sync1 img {
            height: 500px;
            object-fit: cover;
            object-position: center;
        }

        .demo .sync2 img {
            height: 150px;
            object-fit: cover;
            object-position: center;
        }

        footer {
            background-color: #0A142F;
            color: white;
        }

        footer a {
            color: white !important;
            text-decoration: none !important;
        }

        footer .container {
            padding-top: 50px;
            padding-bottom: 50px;
        }

        footer .container p.header {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: .75rem;
            color: #E8EF4D;
        }

        footer .container p.detail {
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 0;
        }

        footer .container p.detail.alamat {
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 1rem;
        }

        footer .container .sitelink {
            display: flex;
            justify-content: space-between;
        }

        footer .container .sitelink ul {
            list-style: none;
            padding-left: 0;
        }

        footer .container .sitelink ul li {
            font-size: 14px;
            font-weight: 400;
            margin-bottom: .75rem;
        }

        footer .container .copyright {
            font-size: 12px;
            font-weight: 400;
        }

        footer .container .top {
            width: 50px;
            height: 50px;
            border-radius: 100%;
            background-color: #007755;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
        }
    </style>

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "GovernmentOrganization",
            "name": "<?= isset($setting->desa) ? $setting->desa : null ?>",
            "alternateName": "Go Digital Desa",
            "url": "<?= base_url() ?>",
            "logo": "https://gides.id/assets/img/favicon.png"
        }
    </script>

    <script type=“text/javascript” src=“https://platform-api.sharethis.com/js/sharethis.js#property=647181d97cccdc001910bb74&product=inline-share-buttons” async=“async”></script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2417204464444609" crossorigin="anonymous"></script>

    <?php if (getPlatformName() == 'Gides') : ?>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-495ZLQJLPL"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-495ZLQJLPL');
        </script>
    <?php elseif (getPlatformName() == 'GidesManis') : ?>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-YYSG07EWM1"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-YYSG07EWM1');
        </script>
    <?php endif; ?>
</head>

<body>
    <nav class="navbar navbar-expand-md navbar-dark bg-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url('') ?>">
                <div class="row">
                    <div class="col-3">
                        <?php if (isset($setting->logo_desa)) : ?>
                            <img class="img-logo" height="50" src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" />
                        <?php endif; ?>
                    </div>

                    <div class="col-9 align-self-center">
                        <p class="nama-desa">
                            <?= isset($setting->desa) ? $setting->desa : null ?>
                        </p>

                        <p class="kab-desa">
                            <?= isset($setting->kabupaten) ? $setting->kabupaten : null ?>
                        </p>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target=".dual-collapse2">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="navbar-collapse collapse w-100 dual-collapse2">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('pemerintah') ?>">Pemerintah</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('infografi') ?>">Infografis</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('belanja') ?>">Belanja</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('produk/hukum') ?>">Produk Hukum</a>
                    </li>

                    <?php if (!empty($setting->buku_profil_desa)): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('buku-profil-desa') ?>">Profil Desa</a>
                        </li>
                    <?php endif; ?>

                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('berita/all') ?>">Berita</a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('surat') ?>" class="nav-link">Surat</a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-expanded="false">
                            Lainnya
                        </a>

                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="<?= base_url('bantuan-sosial') ?>">
                                <i class="fas fa-hands-helping mr-2"></i>Bantuan Sosial
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="<?= base_url('auth/login') ?>">Login CMS</a>
                            <a class="dropdown-item" href="<?= base_url('pengaduan') ?>">Pengaduan Masyarakat</a>
                            <a class="dropdown-item" href="<?= base_url('guestbook') ?>">Buku Tamu Digital</a>
                            <div class="dropdown-divider"></div>
                            <?php foreach ($this->db->get_where('highlight', array('id_user' => $setting->id_user))->result() as $key => $value) : ?>
                                <a href="<?= base_url('highlight/content/' . $value->id) ?>" class="dropdown-item"><?= $value->judul ?></a>
                            <?php endforeach; ?>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <?php if (isset($content)) : ?>
        <?php $this->load->view($content); ?>
    <?php endif; ?>

    <div class="modal fade" id="hubungi_penjual" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Hubungi Penjual</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="card" style="cursor: pointer;" id="card_hubungi_telepon">
                        <div class="card-body">
                            <div class="float-left">
                                <img src="<?= asset_url() ?>assets/templates/images/telephone.png" alt="" width="30" style="width: 30 !important">
                            </div>

                            <div style="margin-left: 4rem;">
                                <div>Telepon</div>
                                <span id="telepon_penjual"></span>
                            </div>

                            <div class="clearfix"></div>
                        </div>
                    </div>

                    <div class="card mt-2" style="cursor: pointer;" id="card_hubungi_whatsapp">
                        <div class="card-body">
                            <div class="float-left">
                                <img src="<?= asset_url() ?>assets/templates/images/whatsapp.png" alt="" width="50">
                            </div>

                            <div style="margin-left: 4rem;">
                                <div>Whatsapp</div>
                                <span id="whatsapp_penjual"></span>
                            </div>

                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="ModalGlobal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Detail Produk</h5>

                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body" id="content_detail">

                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex flex-row item">
                        <div>
                            <?php if (isset($setting->logo_desa)) : ?>
                                <img class="img-logo" width="50" src="<?= asset_url($setting->logo_desa) ?>" alt="Logo" />
                            <?php endif; ?>
                        </div>

                        <div class="ml-3">
                            <p class="nama-desa mb-0" style="font-size: 20px; color: #E8EF4D; font-weight: 700;"><?= isset($setting->desa) ? $setting->desa : null ?></p>
                            <p class="kab-desa mb-0" style="font-size: 16px;"><?= isset($setting->kabupaten) ? $setting->kabupaten : null ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="item">
                        <p class="header">Alamat</p>
                        <p class="detail alamat"><?= isset($setting->alamat) ? $setting->alamat : null ?></p>

                        <p class="header">Kontak Penting</p>
                        <?php foreach ($kontakpenting as $key => $value) : ?>
                            <p class="detail"><?= $value->nama ?>: <a href="tel:<?= $value->no_telp ?>" class="text-decoration-none text-white"><?= $value->no_telp ?></a></p>
                        <?php endforeach; ?>

                        <p class="detail mt-1" id="email_protection"><?= isset($setting->email) ? base64_encode($setting->email) : null ?></p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="item">
                        <p class="header">Sitemap</p>

                        <div class="sitelink">
                            <ul>
                                <li>
                                    <a href="<?= base_url('pemerintah') ?>" class="text-decoration-none text-white">
                                        Pemerintahan
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('infografi') ?>" class="text-decoration-none text-white">
                                        Infografis
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('belanja') ?>" class="text-decoration-none text-white">
                                        Belanja
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('berita/all') ?>" class="text-decoration-none text-white">
                                        Berita
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('surat') ?>" class="text-decoration-none text-white">
                                        Surat
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('bantuan-sosial') ?>" class="text-decoration-none text-white">
                                        Bantuan Sosial
                                    </a>
                                </li>
                            </ul>

                            <ul>
                                <li>
                                    <a href="<?= isset($setting->facebook) ? validate_sosmedurl($setting->facebook, 'facebook') : 'javascript:;' ?>" target="_blank">Facebok</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->twitter) ? validate_sosmedurl($setting->twitter, 'twitter') : 'javascript:;' ?>" target="_blank">Twitter</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->youtube) ? validate_sosmedurl($setting->youtube, 'youtube') : 'javascript:;' ?>" target="_blank">Youtube</a>
                                </li>
                                <li>
                                    <a href="<?= isset($setting->instagram) ? validate_sosmedurl($setting->instagram, 'instagram') : 'javascript:;' ?>" target="_blank">Instagram</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 text-center">
                    <p class="copyright">&copy; 2022 Go Digital Desa (<?= strtoupper(getPlatformName()) ?>) All Rights reserved</p>
                </div>

                <!-- <div class="item">
                    <div class="top">
                        <i class="fa fa-arrow-up"></i>
            </div>
                </div> -->
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="<?= asset_url() ?>assets/templates/js/bootstrap.min.js"></script>
    <script src="<?= asset_url() ?>assets/templates/js/pie-chart.js"></script>
    <script src="<?= asset_url() ?>assets/templates/plugins/owl/owl.carousel.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/sweetalert/sweetalert.min.js"></script>
    <script src="<?= asset_url() ?>assets/extra-libs/lightbox/dist/js/lightbox.min.js"></script>
    <script src="<?= asset_url() ?>assets/js/ajax-request.js"></script>
    <script src="<?= asset_url() ?>assets/js/script.js"></script>

    <script>
        $(window).scroll(function() {
            var scroll = $(window).scrollTop();

            if (scroll >= 200) {
                $(".navbar").addClass("bg-green");
            } else {
                $(".navbar").removeClass("bg-green");
            }
        });

        function pesan(nomorhp) {
            $('#hubungi_penjual').modal('show');
            $('#whatsapp_penjual, #telepon_penjual').html(nomorhp);
            $('#card_hubungi_whatsapp').attr('onclick', `window.location.href = 'https://api.whatsapp.com/send?phone=${nomorhp}&text=Saya%20Tertarik%20dengan%20jualan%20Anda'`)
            $('#card_hubungi_telepon').attr('onclick', `window.open('tel:${nomorhp}')`);
        }

        window.addEventListener('load', (event) => {
            setTraffic();

            var email = document.getElementById('email_protection');
            var email_address = atob(email.innerHTML);

            email.innerHTML = email_address;
        });

        function setTraffic() {
            $.ajax({
                url: "<?= base_url() . '/traffic/add' ?>",
                method: 'POST',
                dataType: 'json',
                success: function(response) {
                    console.log(response.MESSAGE);
                }
            });
        }

        lightbox.option({
            'resizeDuration': 200,
            'wrapAround': true,
            'maxWidth': 800,
            'maxHeight': 600,
        })
    </script>
</body>

</html>